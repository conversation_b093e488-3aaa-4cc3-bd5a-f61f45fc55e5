// 测试优化后的CIDR函数

function ipToNumber(ip) {
  if (!ip || typeof ip !== 'string') {
    throw new Error('Invalid IP address');
  }
  
  const parts = ip.split('.');
  if (parts.length !== 4) {
    throw new Error('Invalid IP address format');
  }
  
  return parts.reduce((acc, part) => {
    const num = parseInt(part, 10);
    if (isNaN(num) || num < 0 || num > 255) {
      throw new Error('Invalid IP address octet');
    }
    return (acc << 8) + num;
  }, 0) >>> 0;
}

function numberToIp(number) {
  if (typeof number !== 'number' || number < 0 || number > 0xFFFFFFFF) {
    throw new Error('Invalid number for IP conversion');
  }
  
  return [
    (number >>> 24) & 0xff,
    (number >>> 16) & 0xff,
    (number >>> 8) & 0xff,
    number & 0xff,
  ].join('.');
}

function calculateNetworkAddress(gateway, mask) {
  try {
    const gatewayNumber = ipToNumber(gateway);
    const maskNumber = ipToNumber(mask);
    const networkNumber = gatewayNumber & maskNumber;
    return numberToIp(networkNumber);
  } catch (error) {
    console.error('Error calculating network address:', error);
    return '0.0.0.0';
  }
}

function subnetMaskToCIDR(mask) {
  try {
    const maskNumber = ipToNumber(mask);
    
    // 检查是否为有效的子网掩码（连续的1后面跟连续的0）
    const binaryMask = maskNumber.toString(2).padStart(32, '0');
    const match = binaryMask.match(/^(1*)(0*)$/);
    
    if (!match) {
      throw new Error('Invalid subnet mask - not contiguous');
    }
    
    // 计算连续1的个数
    const prefixLength = match[1].length;
    
    // 验证：确保没有在0后面出现1
    if (match[2].length + prefixLength !== 32) {
      throw new Error('Invalid subnet mask format');
    }
    
    return prefixLength;
  } catch (error) {
    console.error('Error converting subnet mask to CIDR:', error);
    return 0;
  }
}

function gatewayToCIDR(gateway, mask) {
  try {
    if (!gateway || !mask) {
      throw new Error('Gateway and mask are required');
    }
    
    const networkAddress = calculateNetworkAddress(gateway, mask);
    const cidr = subnetMaskToCIDR(mask);
    
    if (cidr === 0 && mask !== '0.0.0.0') {
      throw new Error('Invalid subnet mask');
    }
    
    return `${networkAddress}/${cidr}`;
  } catch (error) {
    console.error('Error converting to CIDR:', error);
    return '0.0.0.0/0';
  }
}

// 测试用例
console.log('=== CIDR函数测试 ===');

// 测试1: 标准的/24网络
console.log('测试1 - 标准/24网络:');
console.log('输入: gateway=***********, mask=*************');
console.log('输出:', gatewayToCIDR('***********', '*************'));
console.log('期望: ***********/24\n');

// 测试2: /1网络 (你关心的情况)
console.log('测试2 - /1网络:');
console.log('输入: gateway=***********, mask=*********');
console.log('输出:', gatewayToCIDR('***********', '*********'));
console.log('期望: *********/1\n');

// 测试3: /16网络
console.log('测试3 - /16网络:');
console.log('输入: gateway=********, mask=***********');
console.log('输出:', gatewayToCIDR('********', '***********'));
console.log('期望: 10.0.0.0/16\n');

// 测试4: /8网络
console.log('测试4 - /8网络:');
console.log('输入: gateway=************, mask=255.0.0.0');
console.log('输出:', gatewayToCIDR('************', '255.0.0.0'));
console.log('期望: 10.0.0.0/8\n');

// 测试5: /30网络
console.log('测试5 - /30网络:');
console.log('输入: gateway=***********, mask=255.255.255.252');
console.log('输出:', gatewayToCIDR('***********', '255.255.255.252'));
console.log('期望: ***********/30\n');

// 测试6: 错误输入
console.log('测试6 - 错误的子网掩码:');
console.log('输入: gateway=***********, mask=255.255.255.1');
console.log('输出:', gatewayToCIDR('***********', '255.255.255.1'));
console.log('期望: 错误处理\n');

// 测试各个子网掩码对应的CIDR
console.log('=== 子网掩码到CIDR转换测试 ===');
const testMasks = [
  '*********',      // /1
  '192.0.0.0',      // /2
  '224.0.0.0',      // /3
  '240.0.0.0',      // /4
  '255.0.0.0',      // /8
  '***********',    // /16
  '*************',  // /24
  '255.255.255.252' // /30
];

testMasks.forEach(mask => {
  console.log(`${mask} -> /${subnetMaskToCIDR(mask)}`);
});
