<!DOCTYPE html>
<html>
<head>
    <title>CIDR 边界情况测试</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        #output { white-space: pre-line; }
        .error { color: red; }
        .warning { color: orange; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>CIDR 计算边界情况测试</h1>
    <div id="output"></div>
    <script>

function ipToNumber(ip) {
  return ip.split('.').reduce((acc, part) => (acc << 8) + parseInt(part, 10), 0);
}

function numberToIp(number) {
  return [
    (number >>> 24) & 0xff,
    (number >>> 16) & 0xff,
    (number >>> 8) & 0xff,
    number & 0xff,
  ].join('.');
}

function calculateNetworkAddress(gateway, mask) {
  const gatewayNumber = ipToNumber(gateway);
  const maskNumber = ipToNumber(mask);
  const networkNumber = gatewayNumber & maskNumber;
  return numberToIp(networkNumber);
}

// 验证子网掩码是否有效
function isValidSubnetMask(mask) {
  const maskNumber = ipToNumber(mask);
  const binary = maskNumber.toString(2).padStart(32, '0');

  // 检查是否是连续的1后跟连续的0
  const firstZero = binary.indexOf('0');
  if (firstZero === -1) return true; // 全是1，有效

  // 检查第一个0之后是否还有1
  return binary.substring(firstZero).indexOf('1') === -1;
}

// 改进后的实现
function subnetMaskToCIDR(mask) {
  if (!isValidSubnetMask(mask)) {
    console.warn(`无效的子网掩码: ${mask}`);
    return 0;
  }

  const maskNumber = ipToNumber(mask);
  const binary = maskNumber.toString(2).padStart(32, '0');

  // 计算连续的1的个数（从左开始）
  const firstZero = binary.indexOf('0');
  return firstZero === -1 ? 32 : firstZero;
}

function gatewayToCIDR(gateway, mask) {
  if (!isValidSubnetMask(mask)) {
    console.warn(`无效的子网掩码: ${mask}，使用默认值 /24`);
    return `${calculateNetworkAddress(gateway, '*************')}/24`;
  }

  const networkAddress = calculateNetworkAddress(gateway, mask);
  const cidr = subnetMaskToCIDR(mask);

  // 对于异常的CIDR值进行额外检查
  if (cidr < 8 || cidr > 30) {
    console.warn(`异常的CIDR前缀长度: /${cidr}，请检查子网掩码: ${mask}`);
  }

  return `${networkAddress}/${cidr}`;
}

// 验证子网掩码是否有效
function isValidSubnetMask(mask) {
  const maskNumber = ipToNumber(mask);
  const binary = maskNumber.toString(2).padStart(32, '0');
  
  // 检查是否是连续的1后跟连续的0
  const firstZero = binary.indexOf('0');
  if (firstZero === -1) return true; // 全是1，有效
  
  // 检查第一个0之后是否还有1
  return binary.substring(firstZero).indexOf('1') === -1;
}

const output = document.getElementById('output');
function log(text, className = '') {
  const span = document.createElement('span');
  span.className = className;
  span.textContent = text + '\n';
  output.appendChild(span);
}

log('测试可能导致 ***********/1 的情况：');
log('='.repeat(60));

// 测试各种可能导致异常结果的情况
const edgeCases = [
  // 正常情况
  { gateway: '***********', mask: '*************', desc: '正常 /24 网络' },
  { gateway: '***********', mask: '***********', desc: '正常 /16 网络' },
  
  // 可能导致 /1 的异常掩码
  { gateway: '***********', mask: '*********', desc: '掩码 ********* (应该是 /1)' },
  { gateway: '***********', mask: '*********', desc: '无效掩码 *********' },
  { gateway: '***********', mask: '*********', desc: '无效掩码 *********' },
  
  // 其他异常情况
  { gateway: '***********', mask: '0.0.0.0', desc: '全零掩码' },
  { gateway: '***********', mask: '***************', desc: '全一掩码 /32' },
  { gateway: '***********', mask: '*************', desc: '无效掩码 *************' },
  { gateway: '***********', mask: '*************', desc: '掩码 ************* (/23)' },
];

edgeCases.forEach((testCase, index) => {
  const { gateway, mask, desc } = testCase;
  
  try {
    const result = gatewayToCIDR(gateway, mask);
    const isValid = isValidSubnetMask(mask);
    const maskNumber = ipToNumber(mask);
    const binary = maskNumber.toString(2).padStart(32, '0');
    
    log(`测试 ${index + 1}: ${desc}`);
    log(`  网关: ${gateway}, 掩码: ${mask}`);
    log(`  掩码二进制: ${binary}`);
    log(`  掩码有效性: ${isValid ? '有效' : '无效'}`, isValid ? 'success' : 'error');
    log(`  计算结果: ${result}`, result.includes('/1') ? 'warning' : 'success');
    
    if (result === '***********/1') {
      log(`  ⚠️  发现问题结果: ${result}`, 'error');
    }
    
    log('');
  } catch (error) {
    log(`测试 ${index + 1}: ${desc} - 错误: ${error.message}`, 'error');
    log('');
  }
});

// 专门测试可能导致 /1 的情况
log('专门测试导致 /1 的掩码：');
log('='.repeat(60));

const masksThatGive1 = [
  '*********',    // 10000000.00000000.00000000.00000000
  '*********',    // 无效掩码
  '*********',    // 无效掩码
];

masksThatGive1.forEach(mask => {
  const gateway = '***********';
  const result = gatewayToCIDR(gateway, mask);
  const isValid = isValidSubnetMask(mask);
  const maskNumber = ipToNumber(mask);
  const binary = maskNumber.toString(2).padStart(32, '0');
  const onesCount = binary.replace(/0/g, '').length;
  
  log(`掩码: ${mask}`);
  log(`  二进制: ${binary}`);
  log(`  1的个数: ${onesCount}`);
  log(`  有效性: ${isValid ? '有效' : '无效'}`, isValid ? 'success' : 'error');
  log(`  结果: ${result}`, result.includes('/1') ? 'warning' : 'success');
  log('');
});

</script>
</body>
</html>
